#include "GPS_app.h"

uint16_t point1 = 0;
float longitude;
float latitude;

// GPS数据滤波相关变量
typedef struct {
    float lat_history[5];
    float lon_history[5];
    uint8_t history_index;
    uint8_t history_count;
    float last_valid_lat;
    float last_valid_lon;
    uint8_t filter_initialized;
    uint32_t last_valid_time;      // 最后一次有效数据的时间
    uint32_t invalid_count;        // 连续无效数据计数
    uint8_t position_locked;       // 位置锁定标志
    float locked_lat;              // 锁定的纬度
    float locked_lon;              // 锁定的经度
} GPSFilter_t;

static GPSFilter_t gps_filter = {0};

SaveData_t Save_Data;

LatitudeAndLongitude_t g_LatAndLongData = {
    .E_W = 0,
    .N_S = 0,
    .latitude = 0.0,
    .longitude = 0.0
};

char USART_RX_BUF[GPS_BUFFER_LENGTH];
uint8_t uart_GPS_RX_Buff;
extern uint8_t uart3_rx_dma_buffer[UART3_BUFFER_SIZE];
extern uint8_t uart3_ring_buffer_input[UART3_BUFFER_SIZE];
extern struct rt_ringbuffer uart3_ring_buffer;
extern uint8_t uart3_data_buffer[UART3_BUFFER_SIZE];

void GPS_Init(void)
{
    clrStruct();
    rt_ringbuffer_init(&uart3_ring_buffer, uart3_ring_buffer_input, UART3_BUFFER_SIZE);
    HAL_UARTEx_ReceiveToIdle_DMA(&huart3, uart3_rx_dma_buffer, UART3_BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_usart3_rx, DMA_IT_HT);
}

void GPS_Task(void)
{
    uint16_t data_len = rt_ringbuffer_data_len(&uart3_ring_buffer);
    static uint32_t last_gps_data_time = 0;
    static uint32_t last_status_report = 0;
    uint32_t current_time = HAL_GetTick();

    if (data_len > 0) {
        last_gps_data_time = current_time;
        rt_ringbuffer_get(&uart3_ring_buffer, uart3_data_buffer, data_len);

        // GPS原始数据输出（用于调试GPS接收问题）
        my_printf(&huart1, "📡 GPS Raw data (%d bytes): ", data_len);
        for (uint16_t i = 0; i < data_len && i < 80; i++) {
            if (uart3_data_buffer[i] >= 32 && uart3_data_buffer[i] <= 126) {
                my_printf(&huart1, "%c", uart3_data_buffer[i]);
            } else {
                my_printf(&huart1, "[%02X]", uart3_data_buffer[i]);
            }
        }
        my_printf(&huart1, "\r\n");
        
        for (uint16_t i = 0; i < data_len; i++) {
            uart_GPS_RX_Buff = uart3_data_buffer[i];
            if (uart_GPS_RX_Buff == '$') {
                point1 = 0;
            }
            USART_RX_BUF[point1++] = uart_GPS_RX_Buff;
            if (USART_RX_BUF[0] == '$' && USART_RX_BUF[4] == 'M' && USART_RX_BUF[5] == 'C') {
                if (uart_GPS_RX_Buff == '\n') {
                    for (uint16_t j = 0; j < GPS_BUFFER_LENGTH; j++) {
                        Save_Data.GPS_Buffer[j] = 0;
                    }
                    for (uint16_t j = 0; j < point1; j++) {
                        Save_Data.GPS_Buffer[j] = USART_RX_BUF[j];
                    }
                    Save_Data.isGetData = 1;
                    point1 = 0;
                    for (uint16_t j = 0; j < GPS_BUFFER_LENGTH; j++) {
                        USART_RX_BUF[j] = 0;
                    }
                }
            }
            if (point1 >= GPS_BUFFER_LENGTH) {
                point1 = GPS_BUFFER_LENGTH - 1;
            }
        }
    }
    parseGpsBuffer();
    printGpsBuffer();
    // GPS状态报告（暂时注释以减少串口占用）
    // if (current_time - last_status_report > 30000) {
    //     if (current_time - last_gps_data_time > 10000) {
    //         my_printf(&huart1, "GPS Status: No data received for %lu seconds\r\n",
    //                  (current_time - last_gps_data_time) / 1000);
    //         my_printf(&huart1, "Check GPS module connection and antenna\r\n");
    //     } else {
    //         my_printf(&huart1, "GPS Status: Receiving data normally\r\n");
    //     }
    //     last_status_report = current_time;
    // }
}

void GPS_Test_SimulateData(void)
{
    char test_nmea[] = "$GPRMC,123519.00,A,4807.038,N,01131.000,E,022.4,084.4,230394,003.1,W*6A\r\n";
    for (uint16_t j = 0; j < GPS_BUFFER_LENGTH; j++) {
        Save_Data.GPS_Buffer[j] = 0;
    }
    uint16_t len = 0;
    while (test_nmea[len] != '\0' && len < GPS_BUFFER_LENGTH - 1) {
        Save_Data.GPS_Buffer[len] = test_nmea[len];
        len++;
    }
    Save_Data.isGetData = 1;
    parseGpsBuffer();
    printGpsBuffer();
}

void clrStruct(void)
{
    Save_Data.isGetData = 0;
    Save_Data.isParseData = 0;
    Save_Data.isUsefull = 0;
    for (uint16_t i = 0; i < GPS_BUFFER_LENGTH; i++) {
        Save_Data.GPS_Buffer[i] = 0;
    }
    for (uint16_t i = 0; i < UTCTIME_LENGTH; i++) {
        Save_Data.UTCTime[i] = 0;
    }
    for (uint16_t i = 0; i < LATITUDE_LENGTH; i++) {
        Save_Data.latitude[i] = 0;
    }
    for (uint16_t i = 0; i < N_S_LENGTH; i++) {
        Save_Data.N_S[i] = 0;
    }
    for (uint16_t i = 0; i < LONGITUDE_LENGTH; i++) {
        Save_Data.longitude[i] = 0;
    }
    for (uint16_t i = 0; i < E_W_LENGTH; i++) {
        Save_Data.E_W[i] = 0;
    }
}

void parseGpsBuffer(void)
{
    char *subString;
    char *subStringNext;
    char i = 0;
    uint16_t Number = 0, Integer = 0, Decimal = 0;
    if (Save_Data.isGetData)
    {
        Save_Data.isGetData = 0;
        for (i = 0; i <= 6; i++)
        {
            if (i == 0)
            {
                subString = Save_Data.GPS_Buffer;
                while (*subString != ',' && *subString != '\0') {
                    subString++;
                }
                if (*subString == '\0') return;
            }
            else
            {
                subString++;
                subStringNext = subString;
                while (*subStringNext != ',' && *subStringNext != '\0') {
                    subStringNext++;
                }
                if (*subStringNext != '\0')
                {
                    char usefullBuffer[2] = {0};
                    uint16_t len = subStringNext - subString;

                    switch (i)
                    {
                        case 1:
                            if (len < UTCTIME_LENGTH) {
                                for (uint16_t j = 0; j < len; j++) {
                                    Save_Data.UTCTime[j] = subString[j];
                                }
                            }
                            break;
                        case 2:
                            if (len < 2) {
                                usefullBuffer[0] = subString[0];
                            }
                            break;
                        case 3:
                            if (len < LATITUDE_LENGTH) {
                                for (uint16_t j = 0; j < len; j++) {
                                    Save_Data.latitude[j] = subString[j];
                                }
                            }
                            break;
                        case 4:
                            if (len < N_S_LENGTH) {
                                for (uint16_t j = 0; j < len; j++) {
                                    Save_Data.N_S[j] = subString[j];
                                }
                            }
                            break;
                        case 5:
                            if (len < LONGITUDE_LENGTH) {
                                for (uint16_t j = 0; j < len; j++) {
                                    Save_Data.longitude[j] = subString[j];
                                }
                            }
                            break;
                        case 6:
                            if (len < E_W_LENGTH) {
                                for (uint16_t j = 0; j < len; j++) {
                                    Save_Data.E_W[j] = subString[j];
                                }
                            }
                            break;
                        default:
                            break;
                    }
                    subString = subStringNext;
                    Save_Data.isParseData = 1;
                    if (usefullBuffer[0] == 'A')
                        Save_Data.isUsefull = 1;
                    else if (usefullBuffer[0] == 'V')
                        Save_Data.isUsefull = 0;
                }
            }
        }

        if (Save_Data.isParseData)
        {
            if (Save_Data.isUsefull)
            {
                g_LatAndLongData.N_S = Save_Data.N_S[0];
                g_LatAndLongData.E_W = Save_Data.E_W[0];
                for (uint8_t i = 0; i < 9; i++)
                {
                    if (Save_Data.latitude[i] == '\0') break;
                    if (i < 2)
                    {
                        Number *= 10;
                        Number += Save_Data.latitude[i] - '0';
                    }
                    else if (i < 4)
                    {
                        Integer *= 10;
                        Integer += Save_Data.latitude[i] - '0';
                    }
                    else if (i == 4);
                    else if (i < 9)
                    {
                        Decimal *= 10;
                        Decimal += Save_Data.latitude[i] - '0';
                    }
                }
                g_LatAndLongData.latitude = 1.0 * Number + (1.0 * Integer + 1.0 * Decimal / 10000.0) / 60.0;

                Number = 0;
                Integer = 0;
                Decimal = 0;
                for (uint8_t i = 0; i < 10; i++)
                {
                    if (Save_Data.longitude[i] == '\0') break;
                    if (i < 3)
                    {
                        Number *= 10;
                        Number += Save_Data.longitude[i] - '0';
                    }
                    else if (i < 5)
                    {
                        Integer *= 10;
                        Integer += Save_Data.longitude[i] - '0';
                    }
                    else if (i == 5);
                    else if (i < 10)
                    {
                        Decimal *= 10;
                        Decimal += Save_Data.longitude[i] - '0';
                    }
                }
                g_LatAndLongData.longitude = 1.0 * Number + (1.0 * Integer + 1.0 * Decimal / 10000.0) / 60.0;
                longitude = g_LatAndLongData.longitude;
                latitude = g_LatAndLongData.latitude;
                if (g_LatAndLongData.E_W == 'W')
                    longitude = -longitude;
                if (g_LatAndLongData.N_S == 'S')
                    latitude = -latitude;

                // GPS数据验证和调试
                if (latitude < 20.0 || latitude > 55.0 || longitude < 70.0 || longitude > 140.0) {
                    my_printf(&huart1, "⚠️ GPS数据异常: lat=%.6f, lon=%.6f\r\n", latitude, longitude);
                    my_printf(&huart1, "   原始数据: %s %s %s %s\r\n",
                             Save_Data.latitude, Save_Data.N_S, Save_Data.longitude, Save_Data.E_W);
                    my_printf(&huart1, "   解析: Number=%d, Integer=%d, Decimal=%d\r\n", Number, Integer, Decimal);
                    Save_Data.isUsefull = 0;  // 标记为无效数据
                } else {
                    my_printf(&huart1, "✅ GPS数据有效: %.6f°N, %.6f°E\r\n", latitude, longitude);
                }
            }
        }
    }
}

void printGpsBuffer(void)
{
    static uint32_t last_upload_time = 0;
    uint32_t current_time = HAL_GetTick();

    if (Save_Data.isParseData)
    {
        Save_Data.isParseData = 0;
        my_printf(&huart1, "UTC Time = %s\r\n", Save_Data.UTCTime);
        if (Save_Data.isUsefull)
        {
            // 不要重置isUsefull标志，保持GPS数据有效状态
            // GPS解析输出（暂时注释以减少串口占用）
            // my_printf(&huart1, "Real GPS: Latitude: %c, %.6f\r\n", g_LatAndLongData.N_S, g_LatAndLongData.latitude);
            // my_printf(&huart1, "Real GPS: Longitude: %c, %.6f\r\n", g_LatAndLongData.E_W, g_LatAndLongData.longitude);
            // my_printf(&huart1, "Final GPS: %.6f, %.6f\r\n", latitude, longitude);
            // my_printf(&huart1, "------------------------\r\n");

            // 每5秒上传一次GPS数据到高德地图
            if (current_time - last_upload_time >= 5000) {
                GPS_UploadToAMap();
                last_upload_time = current_time;
            }
        }
        else
        {
            // my_printf(&huart1, "GPS DATA is not useful! (No satellite fix)\r\n");
        }
    }
}

typedef struct {
    float base_latitude;
    float base_longitude;
    float base_altitude;
    float movement_radius;
    uint8_t movement_enabled;
    uint32_t update_counter;
    uint8_t initialized;
} VirtualGPS_t;

static VirtualGPS_t virtual_gps = {0};

/**
 * @brief GPS数据滤波函数
 * @param lat 输入纬度
 * @param lon 输入经度
 * @param filtered_lat 输出滤波后纬度
 * @param filtered_lon 输出滤波后经度
 * @return 1表示数据有效，0表示数据被过滤
 */
uint8_t GPS_FilterData(float lat, float lon, float *filtered_lat, float *filtered_lon)
{
    uint32_t current_time = HAL_GetTick();

    // 基本有效性检查 - 使用中国范围的坐标
    if (lat < MIN_VALID_LAT || lat > MAX_VALID_LAT ||
        lon < MIN_VALID_LON || lon > MAX_VALID_LON) {
        my_printf(&huart1, "⚠️ GPS坐标超出中国有效范围: %.6f, %.6f\r\n", lat, lon);
        gps_filter.invalid_count++;

        // 如果连续无效数据过多，锁定位置
        if (gps_filter.invalid_count >= MAX_INVALID_COUNT && !gps_filter.position_locked) {
            GPS_LockPosition();
        }

        // 返回锁定位置或上次有效位置
        if (gps_filter.position_locked) {
            *filtered_lat = gps_filter.locked_lat;
            *filtered_lon = gps_filter.locked_lon;
            my_printf(&huart1, "🔒 使用锁定位置: %.6f, %.6f\r\n", *filtered_lat, *filtered_lon);
        } else if (gps_filter.filter_initialized) {
            *filtered_lat = gps_filter.last_valid_lat;
            *filtered_lon = gps_filter.last_valid_lon;
            my_printf(&huart1, "📍 使用上次有效位置: %.6f, %.6f\r\n", *filtered_lat, *filtered_lon);
        } else {
            return 0;
        }
        return 1;  // 返回有效数据（锁定或上次有效）
    }

    // 初始化滤波器
    if (!gps_filter.filter_initialized) {
        gps_filter.last_valid_lat = lat;
        gps_filter.last_valid_lon = lon;
        gps_filter.last_valid_time = current_time;
        gps_filter.history_index = 0;
        gps_filter.history_count = 0;
        gps_filter.invalid_count = 0;
        gps_filter.filter_initialized = 1;
        *filtered_lat = lat;
        *filtered_lon = lon;
        my_printf(&huart1, "📍 GPS滤波器初始化: %.6f, %.6f\r\n", lat, lon);
        return 1;
    }

    // 计算与上次有效位置的距离
    float lat_diff = lat - gps_filter.last_valid_lat;
    float lon_diff = lon - gps_filter.last_valid_lon;
    float distance = sqrtf(lat_diff * lat_diff + lon_diff * lon_diff);

    // 检查是否为异常跳跃
    if (distance > MAX_JUMP_DISTANCE) {
        my_printf(&huart1, "⚠️ GPS异常跳跃被过滤: %.6f->%.6f, %.6f->%.6f (距离: %.6f)\r\n",
                 gps_filter.last_valid_lat, lat, gps_filter.last_valid_lon, lon, distance);
        gps_filter.invalid_count++;

        // 如果连续无效数据过多，锁定位置
        if (gps_filter.invalid_count >= MAX_INVALID_COUNT && !gps_filter.position_locked) {
            GPS_LockPosition();
        }

        // 返回锁定位置或上次有效位置
        if (gps_filter.position_locked) {
            *filtered_lat = gps_filter.locked_lat;
            *filtered_lon = gps_filter.locked_lon;
        } else {
            *filtered_lat = gps_filter.last_valid_lat;
            *filtered_lon = gps_filter.last_valid_lon;
        }
        return 1;  // 返回稳定位置
    }

    // 数据有效，重置无效计数
    gps_filter.invalid_count = 0;
    gps_filter.last_valid_time = current_time;

    // 添加到历史记录
    gps_filter.lat_history[gps_filter.history_index] = lat;
    gps_filter.lon_history[gps_filter.history_index] = lon;
    gps_filter.history_index = (gps_filter.history_index + 1) % 5;
    if (gps_filter.history_count < 5) {
        gps_filter.history_count++;
    }

    // 计算移动平均
    float avg_lat = 0, avg_lon = 0;
    for (uint8_t i = 0; i < gps_filter.history_count; i++) {
        avg_lat += gps_filter.lat_history[i];
        avg_lon += gps_filter.lon_history[i];
    }
    avg_lat /= gps_filter.history_count;
    avg_lon /= gps_filter.history_count;

    // 更新有效位置
    gps_filter.last_valid_lat = avg_lat;
    gps_filter.last_valid_lon = avg_lon;

    *filtered_lat = avg_lat;
    *filtered_lon = avg_lon;

    return 1;
}

/**
 * @brief 锁定GPS位置 - 当GPS数据不稳定时固定位置
 */
void GPS_LockPosition(void)
{
    if (gps_filter.filter_initialized && gps_filter.last_valid_lat != 0.0f && gps_filter.last_valid_lon != 0.0f) {
        gps_filter.position_locked = 1;
        gps_filter.locked_lat = gps_filter.last_valid_lat;
        gps_filter.locked_lon = gps_filter.last_valid_lon;

        my_printf(&huart1, "🔒 GPS位置已锁定: %.6f°N, %.6f°E\r\n",
                 gps_filter.locked_lat, gps_filter.locked_lon);
        my_printf(&huart1, "💡 位置锁定原因: 连续%d次无效GPS数据\r\n", gps_filter.invalid_count);
        my_printf(&huart1, "📍 系统将使用此固定位置，直到接收到有效GPS信号\r\n");
    } else {
        // 如果没有有效的历史位置，使用衡阳师范学院作为默认位置
        gps_filter.position_locked = 1;
        gps_filter.locked_lat = 26.8812f;   // 衡阳师范学院纬度
        gps_filter.locked_lon = 112.6769f;  // 衡阳师范学院经度

        my_printf(&huart1, "🔒 GPS位置已锁定到默认位置: 衡阳师范学院\r\n");
        my_printf(&huart1, "📍 坐标: %.6f°N, %.6f°E\r\n",
                 gps_filter.locked_lat, gps_filter.locked_lon);
    }
}

/**
 * @brief 解锁GPS位置 - 当接收到有效GPS数据时解锁
 */
void GPS_UnlockPosition(void)
{
    if (gps_filter.position_locked) {
        gps_filter.position_locked = 0;
        gps_filter.invalid_count = 0;

        my_printf(&huart1, "🔓 GPS位置已解锁，恢复实时定位\r\n");
        my_printf(&huart1, "📍 当前位置: %.6f°N, %.6f°E\r\n",
                 gps_filter.last_valid_lat, gps_filter.last_valid_lon);
    }
}

/**
 * @brief 手动设置锁定位置
 */
void GPS_SetLockedPosition(float lat, float lon)
{
    gps_filter.position_locked = 1;
    gps_filter.locked_lat = lat;
    gps_filter.locked_lon = lon;
    gps_filter.invalid_count = 0;

    my_printf(&huart1, "🔒 GPS位置手动锁定: %.6f°N, %.6f°E\r\n", lat, lon);
}

void GPS_Virtual_Init(void)
{
    virtual_gps.base_latitude = 26.8812f;
    virtual_gps.base_longitude = 112.6769f;
    virtual_gps.base_altitude = 65.0f;
    virtual_gps.movement_radius = 0.0f;
    virtual_gps.movement_enabled = 0;
    virtual_gps.update_counter = 0;
    virtual_gps.initialized = 1;
    clrStruct();
    my_printf(&huart1, "Virtual GPS initialized for Hengyang Normal University\r\n");
    my_printf(&huart1, "Base Location: %.5fN, %.5fE, %.1fm\r\n",
              virtual_gps.base_latitude, virtual_gps.base_longitude, virtual_gps.base_altitude);
}

void GPS_Virtual_SetLocation(float lat, float lon, float alt)
{
    if (!virtual_gps.initialized) {
        GPS_Virtual_Init();
    }
    virtual_gps.base_latitude = lat;
    virtual_gps.base_longitude = lon;
    virtual_gps.base_altitude = alt;
    my_printf(&huart1, "Virtual GPS location updated: %.6fN, %.6fE, %.1fm\r\n", lat, lon, alt);
}

void GPS_Virtual_EnableMovement(uint8_t enable)
{
    virtual_gps.movement_enabled = enable;
    my_printf(&huart1, "Virtual GPS movement: %s\r\n", enable ? "ENABLED" : "DISABLED");
}

void GPS_Virtual_GenerateData(void)
{
    if (!virtual_gps.initialized) {
        GPS_Virtual_Init();
    }
    // 使用衡阳师范学院的精确坐标
    float current_lat = 26.8812f;   // 衡阳师范学院纬度
    float current_lon = 112.6769f;  // 衡阳师范学院经度
    float current_alt = virtual_gps.base_altitude;
    uint32_t tick = HAL_GetTick();
    uint32_t seconds = (tick / 1000) % 86400;
    uint8_t hour = (seconds / 3600) % 24;
    uint8_t minute = (seconds / 60) % 60;
    uint8_t sec = seconds % 60;
    int lat_deg = (int)current_lat;
    float lat_min = (current_lat - lat_deg) * 60.0f;
    int lon_deg = (int)current_lon;
    float lon_min = (current_lon - lon_deg) * 60.0f;
    char nmea_sentence[200];
    snprintf(nmea_sentence, sizeof(nmea_sentence),
             "$GPRMC,%02d%02d%02d.00,A,%02d%07.4f,N,%03d%07.4f,E,0.0,0.0,220725,,,A*",
             hour, minute, sec,
             lat_deg, lat_min,
             lon_deg, lon_min);
    uint8_t checksum = 0;
    for (int i = 1; i < strlen(nmea_sentence) - 1; i++) {
        if (nmea_sentence[i] == '*') break;
        checksum ^= nmea_sentence[i];
    }
    char final_sentence[220];
    snprintf(final_sentence, sizeof(final_sentence), "%s%02X\r\n", nmea_sentence, checksum);

    strncpy(Save_Data.GPS_Buffer, final_sentence, GPS_BUFFER_LENGTH - 1);
    Save_Data.GPS_Buffer[GPS_BUFFER_LENGTH - 1] = '\0';
    Save_Data.isGetData = 1;
    latitude = current_lat;
    longitude = current_lon;
    g_LatAndLongData.latitude = current_lat;
    g_LatAndLongData.longitude = current_lon;
    g_LatAndLongData.N_S = 'N';
    g_LatAndLongData.E_W = 'E';
    Save_Data.isUsefull = 1;  // 标记虚拟GPS数据为有效
    virtual_gps.update_counter++;

    // 注意：虚拟GPS不自动上传，由主循环控制上传频率
    // GPS_UploadToAMap(); // 移除自动上传

    // 减少调试输出频率，每30秒输出一次
    if (virtual_gps.update_counter % 30 == 0) {
        my_printf(&huart1, "📍 Virtual GPS: Hengyang Normal University %.6f°N, %.6f°E, %.1fm [Update #%lu]\r\n",
                  current_lat, current_lon, current_alt, virtual_gps.update_counter);
        my_printf(&huart1, "🔒 Position: FIXED - High precision coordinates\r\n");
        my_printf(&huart1, "ℹ️ 虚拟GPS数据已生成，等待主循环上传\r\n");
    }
}

/**
 * @brief 上传GPS数据到高德地图
 * 通过串口1发送格式化的GPS数据供PC端高德地图网页接收
 */
void GPS_UploadToAMap(void)
{
    static uint32_t upload_count = 0;
    char gps_data[128];
    float current_lat, current_lon, current_alt = 68.0;
    float filtered_lat, filtered_lon;
    uint8_t gps_valid = 0;

    // 严格检查GPS数据有效性
    if (Save_Data.isUsefull &&
        g_LatAndLongData.latitude != 0.0 &&
        g_LatAndLongData.longitude != 0.0 &&
        g_LatAndLongData.latitude >= -90.0 && g_LatAndLongData.latitude <= 90.0 &&
        g_LatAndLongData.longitude >= -180.0 && g_LatAndLongData.longitude <= 180.0) {

        // 使用GPS滤波器处理数据
        current_lat = latitude;
        current_lon = longitude;

        if (GPS_FilterData(current_lat, current_lon, &filtered_lat, &filtered_lon)) {
            // 使用滤波后的数据
            current_lat = filtered_lat;
            current_lon = filtered_lon;
            gps_valid = 1;

            my_printf(&huart1, "📡 GPS数据已滤波，上传稳定数据: %.6f, %.6f\r\n", current_lat, current_lon);
        } else {
            my_printf(&huart1, "⚠️ GPS数据被滤波器拒绝，使用上次有效位置\r\n");
            current_lat = filtered_lat;  // 滤波器返回的上次有效位置
            current_lon = filtered_lon;
            gps_valid = 1;  // 仍然上传，但使用稳定的位置
        }

    } else {
        // GPS模块未工作或数据无效，检查是否有锁定位置
        if (gps_filter.position_locked) {
            // 使用锁定位置
            current_lat = gps_filter.locked_lat;
            current_lon = gps_filter.locked_lon;
            gps_valid = 1;

            my_printf(&huart1, "🔒 GPS数据无效，使用锁定位置: %.6f, %.6f\r\n", current_lat, current_lon);
        } else {
            // 没有锁定位置，尝试锁定当前位置
            my_printf(&huart1, "⚠️ GPS模块未工作或数据无效\r\n");
            my_printf(&huart1, "   isUsefull: %d, lat: %.6f, lon: %.6f\r\n",
                     Save_Data.isUsefull, g_LatAndLongData.latitude, g_LatAndLongData.longitude);

            // 如果有历史有效位置，锁定它
            if (gps_filter.filter_initialized) {
                GPS_LockPosition();
                current_lat = gps_filter.locked_lat;
                current_lon = gps_filter.locked_lon;
                gps_valid = 1;
            } else {
                // 完全没有GPS数据，跳过上传
                my_printf(&huart1, "❌ 无任何有效GPS数据，跳过上传\r\n");
                return;
            }
        }
    }

    // 只有GPS数据有效时才上传
    if (gps_valid) {
        // 格式化GPS数据为高德地图网页可识别的格式
        snprintf(gps_data, sizeof(gps_data), "AMAP_GPS:%.6f,%.6f,%.1f", current_lat, current_lon, current_alt);

        // 通过串口1发送到PC端高德地图网页
        my_printf(&huart1, "%s\r\n", gps_data);

        upload_count++;

        // 每5次上传显示一次统计信息
        if (upload_count % 5 == 0) {
            my_printf(&huart1, "🗺️ GPS已上传到高德地图 #%lu: %.6f°N, %.6f°E\r\n",
                     upload_count, current_lat, current_lon);
            my_printf(&huart1, "🌐 高德地图API Key: 946b162f62c7b5af1892cc6fc00b2ea1\r\n");
            my_printf(&huart1, "🔧 数据已通过滤波器处理，确保稳定性\r\n");
        }
    }
}

/**
 * @brief 手动触发GPS上传
 */
void GPS_ManualUpload(void)
{
    my_printf(&huart1, "🚀 手动触发GPS上传到高德地图...\r\n");
    GPS_UploadToAMap();
}

/**
 * @brief 设置自定义GPS位置并上传
 */
void GPS_SetCustomLocationAndUpload(float lat, float lon, float alt)
{
    // 更新全局GPS数据
    latitude = lat;
    longitude = lon;
    g_LatAndLongData.latitude = lat;
    g_LatAndLongData.longitude = lon;
    g_LatAndLongData.N_S = (lat >= 0) ? 'N' : 'S';
    g_LatAndLongData.E_W = (lon >= 0) ? 'E' : 'W';

    my_printf(&huart1, "📍 自定义位置已设置: %.6f°N, %.6f°E, %.1fm\r\n", lat, lon, alt);

    // 立即上传到高德地图
    GPS_UploadToAMap();
}

/**
 * @brief GPS上传状态报告
 */
void GPS_PrintUploadStatus(void)
{
    my_printf(&huart1, "\r\n=== GPS高德地图上传状态 ===\r\n");
    my_printf(&huart1, "📍 当前位置: %.6f°N, %.6f°E\r\n", latitude, longitude);
    my_printf(&huart1, "🗺️ 高德地图API Key: 946b162f62c7b5af1892cc6fc00b2ea1\r\n");
    my_printf(&huart1, "📊 ThingSpeak频道: 3014831 (API: LU22ZUP4ZTFK4IY9)\r\n");
    my_printf(&huart1, "⏰ 上传间隔: 5秒自动上传\r\n");
    my_printf(&huart1, "📱 数据格式: AMAP_GPS:纬度,经度,高度\r\n");

    // GPS位置锁定状态
    my_printf(&huart1, "\r\n--- GPS位置锁定状态 ---\r\n");
    if (gps_filter.position_locked) {
        my_printf(&huart1, "🔒 位置状态: 已锁定\r\n");
        my_printf(&huart1, "📍 锁定位置: %.6f°N, %.6f°E\r\n",
                 gps_filter.locked_lat, gps_filter.locked_lon);
        my_printf(&huart1, "⚠️ 连续无效数据: %d次\r\n", gps_filter.invalid_count);
    } else {
        my_printf(&huart1, "🔓 位置状态: 实时定位\r\n");
        if (gps_filter.filter_initialized) {
            my_printf(&huart1, "📍 上次有效位置: %.6f°N, %.6f°E\r\n",
                     gps_filter.last_valid_lat, gps_filter.last_valid_lon);
        }
    }

    my_printf(&huart1, "📊 GPS数据状态: %s\r\n", Save_Data.isUsefull ? "有效" : "无效");

    my_printf(&huart1, "\r\n--- 可用命令 ---\r\n");
    my_printf(&huart1, "💡 gps_upload - 手动上传GPS数据\r\n");
    my_printf(&huart1, "💡 gps_lock - 锁定当前GPS位置\r\n");
    my_printf(&huart1, "💡 gps_unlock - 解锁GPS位置\r\n");
    my_printf(&huart1, "💡 gps_lock_here - 锁定到衡阳师范学院\r\n");
    my_printf(&huart1, "💡 gps_lock_sports - 锁定到体育中心\r\n");
    my_printf(&huart1, "💡 gps_lock_wanda - 锁定到万达广场\r\n");
    my_printf(&huart1, "💡 gps_hengyang - 设置体育中心位置\r\n");
    my_printf(&huart1, "============================\r\n\r\n");
}
