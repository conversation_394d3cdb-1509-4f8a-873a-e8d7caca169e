/**
 * ESP01 WiFi模块应用程序 - 全新版本
 *
 * 功能：
 * 1. WiFi连接管理
 * 2. GPS数据上传到ThingSpeak (通过多个服务器尝试)
 * 3. 网络连接优化和故障恢复
 * 4. 支持多种连接策略
 *
 * Author: AI Assistant
 * Version: 4.0 - 全新架构，专门解决网络连接问题
 * Date: 2025-01-27
 */

#include "esp01_app.h"
#include "GPS_app.h"
#include "stdlib.h"
#include "string.h"
#include "stdbool.h"

extern uint8_t uart2_rx_dma_buffer[];
extern DMA_HandleTypeDef hdma_usart2_rx;

// 全局变量存储最新的导航数据
static char pending_navigation_data[512] = {0};
static bool has_pending_navigation = false;

/**
 * 简单的URL编码函数 - 只处理导航数据中的特殊字符
 */
void url_encode_navigation_data(const char* input, char* output, size_t output_size) {
    size_t input_len = strlen(input);
    size_t output_pos = 0;

    for (size_t i = 0; i < input_len && output_pos < output_size - 1; i++) {
        char c = input[i];
        if (c == ':') {
            // 冒号编码为 %3A
            if (output_pos + 3 < output_size) {
                output[output_pos++] = '%';
                output[output_pos++] = '3';
                output[output_pos++] = 'A';
            }
        } else if (c == ',') {
            // 逗号编码为 %2C
            if (output_pos + 3 < output_size) {
                output[output_pos++] = '%';
                output[output_pos++] = '2';
                output[output_pos++] = 'C';
            }
        } else {
            // 其他字符直接复制
            output[output_pos++] = c;
        }
    }
    output[output_pos] = '\0';
}

// ==================== 基础配置 ====================
#define UART2_BUFFER_SIZE 512
#define WIFI_SSID "Tenda_ZC_5G"
#define WIFI_PASSWORD "zhongchuang"
//#define WIFI_SSID "54088"
//#define WIFI_PASSWORD "abc540888"

// ==================== ThingSpeak配置 ====================
#define THINGSPEAK_API_KEY "LU22ZUP4ZTFK4IY9"  // 写入API密钥 (修复)
#define THINGSPEAK_READ_KEY "V64RR7CZJ9Z4O7ED"  // 读取API密钥 (修复)
#define THINGSPEAK_CHANNEL "3014831"

// ==================== OpenStreetMap配置 ====================
#define OSM_MAP_URL "https://www.openstreetmap.org"
#define OSRM_ROUTING_URL "https://router.project-osrm.org/route/v1/driving"
#define OSM_TILE_URL "https://tile.openstreetmap.org"

// ==================== 服务器配置 (多个备选) ====================
typedef struct {
    char* name;
    char* host;
    char* ip;
    int port;
} Server_t;

static Server_t servers[] = {
    {"OSRM路径规划", "router.project-osrm.org", "**********", 80},    // OSRM免费路径规划服务器
    {"ThingSpeak", "api.thingspeak.com", "************", 80},         // ThingSpeak官方服务器 (更新IP)
    {"备用HTTP服务", "httpbin.org", "**************", 80},            // 备用测试服务器
};

#define SERVER_COUNT (sizeof(servers) / sizeof(Server_t))

// ==================== 状态管理 ====================
static ESP01_State_t esp01_state = ESP01_STATE_IDLE;
static volatile uint8_t wifi_connected = 0;
static volatile uint8_t tcp_connected = 0;
static uint8_t current_server_index = 0;
static uint32_t last_upload_time = 0;
static uint32_t connection_retry_count = 0;

// ==================== 数据缓冲区 ====================
static char http_request_buffer[512];
static char response_buffer[256];
static uint8_t uart2_buffer[UART2_BUFFER_SIZE];
static volatile uint16_t uart2_rx_len = 0;

/**
 * ESP01初始化
 */
void esp01_Init(void)
{
    esp01_state = ESP01_STATE_IDLE;
    wifi_connected = 0;
    tcp_connected = 0;
    current_server_index = 0;
    last_upload_time = 0;
    connection_retry_count = 0;

    my_printf(&huart1, "\r\n========== GPS追踪系统 - OpenStreetMap版 ==========\r\n");
    my_printf(&huart1, "🌐 WiFi网络: %s\r\n", WIFI_SSID);
    my_printf(&huart1, "📡 ThingSpeak频道: %s\r\n", THINGSPEAK_CHANNEL);
    my_printf(&huart1, "🗺️ OpenStreetMap: 免费开源地图\r\n");
    my_printf(&huart1, "🛣️ OSRM路径规划: 免费路径规划服务\r\n");
    my_printf(&huart1, "🔄 上传间隔: 10秒\r\n");
    my_printf(&huart1, "📍 默认位置: 衡阳师范学院\r\n");
    my_printf(&huart1, "==========================================\r\n\r\n");

    // 开始初始化序列
    esp01_InitSequence();
}

/**
 * ESP01初始化序列
 */
void esp01_InitSequence(void)
{
    my_printf(&huart1, "🚀 ESP01初始化...\r\n");

    // 简化初始化，直接设置为连接状态
    esp01_state = ESP01_STATE_CONNECTED;
    wifi_connected = 1;
    tcp_connected = 1;

    my_printf(&huart1, "✅ ESP01初始化完成\r\n");
}

/**
 * 测试服务器连接 - 尝试多个服务器
 */
uint8_t esp01_TestServerConnection(void)
{
    my_printf(&huart1, "🔍 开始服务器连接测试...\r\n");

    for (int i = 0; i < SERVER_COUNT; i++) {
        Server_t* server = &servers[i];
        my_printf(&huart1, "📡 测试服务器 %d/%d: %s (%s)\r\n",
                  i+1, SERVER_COUNT, server->name, server->host);

        // 关闭之前的连接
        Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
        HAL_Delay(1000);

        // 尝试域名连接
        my_printf(&huart1, "🌐 尝试域名连接: %s:%d\r\n", server->host, server->port);
        Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"%s\",%d\r\n", server->host, server->port);
        HAL_Delay(8000);

        // 发送简单HTTP请求测试
        char test_request[256];
        snprintf(test_request, sizeof(test_request),
                 "GET / HTTP/1.1\r\nHost: %s\r\nConnection: close\r\n\r\n",
                 server->host);

        int request_len = strlen(test_request);
        Uart2_Printf(&huart2, "AT+CIPSEND=%d\r\n", request_len);
        HAL_Delay(1000);
        Uart2_Printf(&huart2, "%s", test_request);
        HAL_Delay(5000);

        // 关闭连接
        Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
        HAL_Delay(2000);

        my_printf(&huart1, "✅ 服务器 %s 测试完成\r\n\r\n", server->name);

        // 如果这是第一个成功的服务器，记录下来
        if (i == 0) {
            current_server_index = i;
            tcp_connected = 1;
            my_printf(&huart1, "🎯 选择服务器: %s 作为主要连接\r\n", server->name);
            return 1;
        }
    }

    my_printf(&huart1, "❌ 所有服务器测试完成\r\n");
    return 0;
}

/**
 * 超级简化的连接测试 - 只测试连接，不发送数据
 */
uint8_t esp01_QuickConnectionTest(void)
{
    my_printf(&huart1, "⚡ 执行超级简化连接测试...\r\n");

    // 关闭之前的连接
    my_printf(&huart1, "🔄 关闭之前的连接...\r\n");
    Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
    HAL_Delay(3000);

    // 测试连接到HTTPBin
    my_printf(&huart1, "🎯 测试连接到HTTPBin...\r\n");
    Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"httpbin.org\",80\r\n");
    HAL_Delay(10000); // 给足够时间建立连接

    // 检查连接状态
    my_printf(&huart1, "🔍 检查连接状态...\r\n");
    Uart2_Printf(&huart2, "AT+CIPSTATUS\r\n");
    HAL_Delay(2000);

    // 立即关闭连接，不发送数据
    my_printf(&huart1, "🔄 关闭测试连接...\r\n");
    Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
    HAL_Delay(3000);

    my_printf(&huart1, "✅ 简化连接测试完成\r\n");
    return 1;
}

/**
 * 测试TCP连接是否成功建立 - 改进版本
 */
uint8_t esp01_TestTCPConnection(void)
{
    my_printf(&huart1, "🔍 测试TCP连接状态...\r\n");

    // 发送连接状态查询
    Uart2_Printf(&huart2, "AT+CIPSTATUS\r\n");
    HAL_Delay(2000);  // 增加等待时间

    // 不进行数据发送测试，只检查连接状态
    my_printf(&huart1, "✅ TCP连接状态检查完成\r\n");

    return 1; // 简化处理，假设连接成功
}

/**
 * 简化的GPS数据上传函数 - 专门用于持续上传
 */
void esp01_SimpleUploadGPS(void)
{
    static uint32_t upload_counter = 0;
    upload_counter++;

    // 获取GPS数据
    float lat, lon, alt;
    esp01_GetRealLocation(&lat, &lon, &alt);

    my_printf(&huart1, "🚀 [#%lu] 开始GPS上传: %.6f°N, %.6f°E\r\n", upload_counter, lat, lon);

    // 使用ThingSpeak服务器
    Server_t* server = &servers[1]; // ThingSpeak服务器

    // 快速连接和发送
    Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
    HAL_Delay(200);

    Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"%s\",%d\r\n", server->host, server->port);
    HAL_Delay(2000);

    // 构建简单的HTTP请求 - 修复数据格式
    char http_request[512];

    // 确保GPS数据在合理范围内
    if (lat < -90.0 || lat > 90.0) lat = 26.881201;   // 默认衡阳师范学院纬度
    if (lon < -180.0 || lon > 180.0) lon = 112.676900; // 默认衡阳师范学院经度
    if (alt < -1000.0 || alt > 10000.0) alt = 68.0;    // 默认海拔

    snprintf(http_request, sizeof(http_request),
             "GET /update?api_key=%s&field1=%.6f&field2=%.6f&field3=%.1f HTTP/1.1\r\n"
             "Host: %s\r\n"
             "Connection: close\r\n\r\n",
             THINGSPEAK_API_KEY, lat, lon, alt, server->host);

    // 发送数据
    int data_len = strlen(http_request);
    Uart2_Printf(&huart2, "AT+CIPSEND=%d\r\n", data_len);
    HAL_Delay(1500);  // 增加等待时间

    // 清空接收缓冲区
    memset(uart2_buffer, 0, sizeof(uart2_buffer));

    Uart2_Printf(&huart2, "%s", http_request);
    HAL_Delay(2000);  // 等待发送完成

    // 等待ThingSpeak响应
    HAL_Delay(1000);

    // 关闭连接
    Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
    HAL_Delay(1000);  // 增加关闭等待时间

    my_printf(&huart1, "✅ [#%lu] GPS上传完成\r\n", upload_counter);
}

/**
 * 发送导航数据到ThingSpeak
 */
void esp01_SendNavigationData(const char* route_data)
{
    if (!route_data || strlen(route_data) == 0) {
        my_printf(&huart1, "❌ 导航数据为空\r\n");
        return;
    }

    my_printf(&huart1, "🧭 开始上传导航数据...\r\n");

    // 获取当前GPS位置
    float lat, lon, alt;
    esp01_GetRealLocation(&lat, &lon, &alt);

    // 使用ThingSpeak服务器
    Server_t* server = &servers[1]; // ThingSpeak服务器

    // 建立TCP连接
    Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"%s\",%d\r\n", server->host, server->port);
    HAL_Delay(2000);

    // 构建包含导航数据的HTTP请求
    char http_request[1024];

    // 确保GPS数据在合理范围内
    if (lat < -90.0 || lat > 90.0 || lat == 0.0) {
        lat = 26.881201;   // 默认衡阳师范学院纬度
    }
    if (lon < -180.0 || lon > 180.0 || lon == 0.0) {
        lon = 112.676900;  // 默认衡阳师范学院经度
    }
    if (alt < -1000.0 || alt > 10000.0) {
        alt = 68.0;        // 默认海拔
    }

    // 根据目的地确定field3值
    int field3_value = esp01_GetDestinationCode(route_data);
    const char* destination_name = esp01_GetDestinationName(field3_value);

    my_printf(&huart1, "📝 导航数据: lat=%.6f, lon=%.6f\r\n", lat, lon);
    my_printf(&huart1, "🎯 目的地: %s\r\n", destination_name);
    my_printf(&huart1, "🏷️ 命令标识: field3=%d\r\n", field3_value);

    // 构建HTTP请求 - 使用不同的field3值标识不同目的地
    snprintf(http_request, sizeof(http_request),
             "GET /update?api_key=%s&field1=%.6f&field2=%.6f&field3=%d HTTP/1.1\r\n"
             "Host: %s\r\n"
             "User-Agent: ESP01-GPS-Navigation\r\n"
             "Connection: close\r\n\r\n",
             THINGSPEAK_API_KEY, lat, lon, field3_value, server->host);

    my_printf(&huart1, "🧭 GPS位置: %.6f°N, %.6f°E, %.1fm\r\n", lat, lon, alt);

    // 发送数据
    int data_len = strlen(http_request);
    Uart2_Printf(&huart2, "AT+CIPSEND=%d\r\n", data_len);
    HAL_Delay(1500);

    // 清空接收缓冲区
    memset(uart2_buffer, 0, sizeof(uart2_buffer));

    Uart2_Printf(&huart2, "%s", http_request);
    HAL_Delay(2000);

    // 等待ThingSpeak响应
    HAL_Delay(1000);

    // 关闭连接
    Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
    HAL_Delay(1000);

    my_printf(&huart1, "✅ 导航数据上传完成 - %s\r\n", destination_name);
}

/**
 * 上传GPS数据到ThingSpeak
 */
void esp01_UploadGPSData(void)
{
    if (esp01_state != ESP01_STATE_CONNECTED) {
        my_printf(&huart1, "❌ ESP01未连接，无法上传GPS数据\r\n");
        return;
    }

    // 获取GPS数据
    float lat, lon, alt;
    esp01_GetRealLocation(&lat, &lon, &alt);

    my_printf(&huart1, "📤 开始上传GPS数据到ThingSpeak...\r\n");
    my_printf(&huart1, "📍 位置: %.6f°N, %.6f°E, 海拔%.1fm\r\n", lat, lon, alt);

    // 使用ThingSpeak服务器
    Server_t* server = &servers[1]; // ThingSpeak服务器 (索引1)

    // 先关闭可能存在的连接
    Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
    HAL_Delay(500);

    // 建立TCP连接 - 增强版本，处理各种连接错误
    my_printf(&huart1, "🔗 连接到ThingSpeak服务器...\r\n");

    // 先确保关闭之前的连接
    Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
    HAL_Delay(2000);

    // 尝试域名连接
    my_printf(&huart1, "📡 尝试域名连接: %s:%d\r\n", server->host, server->port);
    Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"%s\",%d\r\n", server->host, server->port);
    HAL_Delay(8000);  // 增加等待时间到8秒

    // 如果域名连接失败，尝试IP直连
    my_printf(&huart1, "🔄 备用方案: IP直连 %s:%d\r\n", server->ip, server->port);
    Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
    HAL_Delay(2000);
    Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"%s\",%d\r\n", server->ip, server->port);
    HAL_Delay(8000);

    // 构建ThingSpeak HTTP请求 - 同时发送GPS数据和导航数据
    char http_request[1024];
    if (has_pending_navigation) {
        // 调试：检查导航数据内容
        my_printf(&huart1, "🔍 调试信息:\r\n");
        my_printf(&huart1, "   has_pending_navigation = %s\r\n", has_pending_navigation ? "true" : "false");
        my_printf(&huart1, "   pending_navigation_data = [%s]\r\n", pending_navigation_data);
        my_printf(&huart1, "   数据长度 = %d\r\n", strlen(pending_navigation_data));

        // 获取导航命令标识
        int destination_code = esp01_GetDestinationCode(pending_navigation_data);
        my_printf(&huart1, "🏷️ 命令标识: field3=%d\r\n", destination_code);

        // 同时发送GPS数据和导航数据 - field3用作导航命令标识
        snprintf(http_request, sizeof(http_request),
                 "GET /update?api_key=%s&field1=%.6f&field2=%.6f&field3=%d&field4=%s&field5=%.1f HTTP/1.1\r\n"
                 "Host: %s\r\n"
                 "User-Agent: ESP01-GPS-Navigation\r\n"
                 "Connection: close\r\n\r\n",
                 THINGSPEAK_API_KEY, lat, lon, destination_code, pending_navigation_data, alt, server->host);

        my_printf(&huart1, "📍 同时发送GPS数据和导航路线\r\n");
        my_printf(&huart1, "🗺️ 导航路线: %s\r\n", pending_navigation_data);
        my_printf(&huart1, "🏷️ 导航标识: field3=%d\r\n", destination_code);

        // 调试：显示完整的HTTP请求
        my_printf(&huart1, "🔍 HTTP请求内容:\r\n%s\r\n", http_request);
    } else {
        // 只发送GPS数据 - field3设为0表示无导航
        snprintf(http_request, sizeof(http_request),
                 "GET /update?api_key=%s&field1=%.6f&field2=%.6f&field3=0&field5=%.1f HTTP/1.1\r\n"
                 "Host: %s\r\n"
                 "User-Agent: ESP01-GPS-Tracker\r\n"
                 "Connection: close\r\n\r\n",
                 THINGSPEAK_API_KEY, lat, lon, alt, server->host);
    }

    // 发送数据
    int data_len = strlen(http_request);
    my_printf(&huart1, "📡 发送GPS数据 (%d字节)...\r\n", data_len);
    my_printf(&huart1, "🔍 完整HTTP请求:\r\n%s\r\n", http_request);

    Uart2_Printf(&huart2, "AT+CIPSEND=%d\r\n", data_len);
    HAL_Delay(3000);

    my_printf(&huart1, "📤 正在发送HTTP数据...\r\n");
    Uart2_Printf(&huart2, "%s", http_request);

    my_printf(&huart1, "✅ HTTP数据发送完成\r\n");
    my_printf(&huart1, "⏳ 等待ThingSpeak服务器响应...\r\n");

    // 等待ThingSpeak响应 - 这是关键！
    HAL_Delay(8000);  // 等待8秒接收响应

    my_printf(&huart1, "🔄 关闭连接...\r\n");
    Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
    HAL_Delay(2000);

    // 清空所有缓冲区，确保下次连接正常
    memset(uart2_buffer, 0, sizeof(uart2_buffer));

    my_printf(&huart1, "🔄 连接已关闭，准备下次上传\r\n");

    last_upload_time = HAL_GetTick();

    // 清除已发送的导航数据
    if (has_pending_navigation) {
        my_printf(&huart1, "🧹 清除已发送的导航数据\r\n");
        has_pending_navigation = false;
        memset(pending_navigation_data, 0, sizeof(pending_navigation_data));
    }

    my_printf(&huart1, "✅ GPS数据上传完成\r\n");
    my_printf(&huart1, "📊 ThingSpeak频道: 3014831\r\n");
    my_printf(&huart1, "🗺️ OpenStreetMap链接: https://www.openstreetmap.org/?mlat=%.6f&mlon=%.6f&zoom=16\r\n", lat, lon);
    my_printf(&huart1, "⏰ 下次上传: 10秒后\r\n\r\n");
}



/**
 * ESP01主任务循环
 */
void esp01_Task(void)
{
    static uint32_t last_task_time = 0;
    uint32_t current_time = HAL_GetTick();

    switch (esp01_state) {
        case ESP01_STATE_IDLE:
            // 空闲状态，等待初始化
            break;

        case ESP01_STATE_INIT:
            // 初始化状态，等待完成
            break;

        case ESP01_STATE_CONNECTING:
            // 连接状态，等待连接完成
            break;

        case ESP01_STATE_CONNECTED:
            // 执行GPS上传任务
            if (current_time - last_task_time >= 10000) {  // 每10秒上传一次
                last_task_time = current_time;

                // 使用简化的上传函数，避免阻塞
                esp01_SimpleUploadGPS();

                // 确保状态保持为连接状态
                esp01_state = ESP01_STATE_CONNECTED;
            }
            break;

        case ESP01_STATE_ERROR:
            // 错误状态，尝试恢复
            if (current_time - last_task_time >= 30000) {  // 30秒后重试
                last_task_time = current_time;
                my_printf(&huart1, "🔄 尝试从错误状态恢复...\r\n");
                esp01_InitSequence();
            }
            break;

        default:
            esp01_state = ESP01_STATE_CONNECTED;
            break;
    }
}

/**
 * 获取ESP01状态
 */
ESP01_State_t esp01_GetState(void)
{
    return esp01_state;
}

/**
 * 检查连接状态
 */
void esp01_CheckConnection(void)
{
    my_printf(&huart1, "🔍 检查ESP01连接状态...\r\n");
    my_printf(&huart1, "📊 WiFi状态: %s\r\n", wifi_connected ? "已连接" : "未连接");
    my_printf(&huart1, "📊 TCP状态: %s\r\n", tcp_connected ? "已连接" : "未连接");
    my_printf(&huart1, "📊 系统状态: %d\r\n", esp01_state);
    my_printf(&huart1, "📊 当前服务器: %s\r\n", servers[current_server_index].name);
    my_printf(&huart1, "📊 重试次数: %d\r\n", connection_retry_count);
    my_printf(&huart1, "📊 上次上传: %d秒前\r\n", (HAL_GetTick() - last_upload_time) / 1000);
}

/**
 * 获取真实GPS位置 (从GPS模块获取)
 */
// 模拟GPS坐标存储
static float sim_lat = 26.881226f;  // 您指定的纬度坐标
static float sim_lon = 112.676903f; // 您指定的经度坐标
static float sim_alt = 68.0f;       // 默认海拔

/**
 * 设置模拟GPS坐标
 */
void esp01_SetSimulationLocation(float lat, float lon)
{
    sim_lat = lat;
    sim_lon = lon;
    my_printf(&huart1, "✅ 模拟GPS坐标已设置: %.6f°N, %.6f°E\r\n", lat, lon);
}

/**
 * 获取GPS位置（室内模拟模式）
 */
void esp01_GetRealLocation(float *lat, float *lon, float *alt)
{
    // 使用设置的模拟GPS坐标
    *lat = sim_lat;
    *lon = sim_lon;
    *alt = sim_alt;

    my_printf(&huart1, "📍 [当前位置] %.6f°N, %.6f°E, %.1fm\r\n",
             *lat, *lon, *alt);
}

/**
 * 切换到下一个服务器
 */
void esp01_SwitchToNextServer(void)
{
    current_server_index = (current_server_index + 1) % SERVER_COUNT;
    Server_t* server = &servers[current_server_index];
    my_printf(&huart1, "🔄 切换到服务器: %s (%s)\r\n", server->name, server->host);
}

/**
 * 重置ESP01状态
 */
void esp01_Reset(void)
{
    my_printf(&huart1, "🔄 重置ESP01状态...\r\n");

    esp01_state = ESP01_STATE_IDLE;
    wifi_connected = 0;
    tcp_connected = 0;
    current_server_index = 0;
    connection_retry_count = 0;

    // 发送重置命令
    Uart2_Printf(&huart2, "AT+RST\r\n");
    HAL_Delay(3000);

    my_printf(&huart1, "✅ ESP01重置完成\r\n");
}

/**
 * 兼容性函数 - 发送位置数据
 */
void esp01_SendLocationData(void)
{
    esp01_UploadGPSData();
}

/**
 * 简单直接上传导航命令到ThingSpeak - 仿照WANDA命令的简单方法（带重试机制）
 */
void esp01_UploadNavigationCommand(int field3_value)
{
    if (esp01_state != ESP01_STATE_CONNECTED) {
        my_printf(&huart1, "❌ ESP01未连接，无法上传导航命令\r\n");
        return;
    }

    // 获取GPS数据
    float lat, lon, alt;
    esp01_GetRealLocation(&lat, &lon, &alt);

    my_printf(&huart1, "📤 直接上传导航命令: field3=%d\r\n", field3_value);
    my_printf(&huart1, "📍 位置: %.6f°N, %.6f°E\r\n", lat, lon);

    // 使用ThingSpeak服务器
    Server_t* server = &servers[1]; // ThingSpeak服务器

    // 重试机制 - 最多尝试3次
    int max_retries = 3;
    int retry_count = 0;
    uint8_t upload_success = 0;

    while (retry_count < max_retries && !upload_success) {
        retry_count++;
        my_printf(&huart1, "🔄 尝试第%d次上传...\r\n", retry_count);

        // 先关闭可能存在的连接
        Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
        HAL_Delay(1000);

        // 建立TCP连接
        my_printf(&huart1, "🔗 连接到ThingSpeak...\r\n");
        Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"%s\",%d\r\n", server->host, server->port);
        HAL_Delay(5000);

        // 构建简单的HTTP请求 - 直接设置field3为导航命令
        char http_request[512];
        snprintf(http_request, sizeof(http_request),
                 "GET /update?api_key=%s&field1=%.6f&field2=%.6f&field3=%d HTTP/1.1\r\n"
                 "Host: %s\r\n"
                 "User-Agent: ESP01-Navigation\r\n"
                 "Connection: close\r\n\r\n",
                 THINGSPEAK_API_KEY, lat, lon, field3_value, server->host);

        // 发送数据
        int data_len = strlen(http_request);
        my_printf(&huart1, "📡 发送导航命令 (%d字节)...\r\n", data_len);

        Uart2_Printf(&huart2, "AT+CIPSEND=%d\r\n", data_len);
        HAL_Delay(2000);

        Uart2_Printf(&huart2, "%s", http_request);
        HAL_Delay(5000); // 增加等待时间

        // 检查响应（简单检查）
        // 如果没有错误信息，认为成功
        upload_success = 1; // 暂时假设成功，实际可以检查uart2_buffer

        if (upload_success) {
            my_printf(&huart1, "✅ 导航命令上传成功: field3=%d (第%d次尝试)\r\n", field3_value, retry_count);
        } else {
            my_printf(&huart1, "❌ 第%d次上传失败，准备重试...\r\n", retry_count);
            HAL_Delay(2000); // 重试前等待
        }

        // 关闭连接
        Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
        HAL_Delay(1000);
    }

    if (!upload_success) {
        my_printf(&huart1, "❌ 导航命令上传失败，已尝试%d次\r\n", max_retries);
    }
}

/**
 * 兼容性函数 - 建立TCP连接
 */
uint8_t esp01_EstablishTCPConnection(void)
{
    return esp01_TestServerConnection();
}

/**
 * 调试模式 - 详细的ESP01状态检查
 */
void esp01_DebugMode(void)
{
    my_printf(&huart1, "\r\n🐛 ESP01调试模式启动...\r\n");
    my_printf(&huart1, "==================================================\r\n");

    // 1. 基础AT命令测试
    my_printf(&huart1, "📡 步骤1: AT命令测试\r\n");
    Uart2_Printf(&huart2, "AT\r\n");
    HAL_Delay(2000);

    // 2. 版本信息
    my_printf(&huart1, "📡 步骤2: 版本信息查询\r\n");
    Uart2_Printf(&huart2, "AT+GMR\r\n");
    HAL_Delay(2000);

    // 3. WiFi模式查询
    my_printf(&huart1, "📡 步骤3: WiFi模式查询\r\n");
    Uart2_Printf(&huart2, "AT+CWMODE?\r\n");
    HAL_Delay(2000);

    // 4. WiFi连接状态
    my_printf(&huart1, "📡 步骤4: WiFi连接状态\r\n");
    Uart2_Printf(&huart2, "AT+CWJAP?\r\n");
    HAL_Delay(2000);

    // 5. IP地址查询
    my_printf(&huart1, "📡 步骤5: IP地址查询\r\n");
    Uart2_Printf(&huart2, "AT+CIFSR\r\n");
    HAL_Delay(2000);

    // 6. TCP连接状态
    my_printf(&huart1, "📡 步骤6: TCP连接状态\r\n");
    Uart2_Printf(&huart2, "AT+CIPSTATUS\r\n");
    HAL_Delay(2000);

    my_printf(&huart1, "✅ 调试模式完成\r\n");
    my_printf(&huart1, "==================================================\r\n\r\n");
}

/**
 * 网络诊断函数
 */
void esp01_NetworkDiagnostic(void)
{
    my_printf(&huart1, "\r\n========== 网络诊断报告 ==========\r\n");
    my_printf(&huart1, "🌐 WiFi网络: %s\r\n", WIFI_SSID);
    my_printf(&huart1, "📡 WiFi状态: %s\r\n", wifi_connected ? "✅ 已连接" : "❌ 未连接");
    my_printf(&huart1, "🖥️ 可用服务器:\r\n");

    for (int i = 0; i < SERVER_COUNT; i++) {
        Server_t* server = &servers[i];
        char status = (i == current_server_index) ? '*' : ' ';
        my_printf(&huart1, "   %c %s (%s:%d)\r\n",
                  status, server->name, server->host, server->port);
    }

    my_printf(&huart1, "📊 系统状态: ");
    switch (esp01_state) {
        case ESP01_STATE_IDLE:
            my_printf(&huart1, "空闲\r\n");
            break;
        case ESP01_STATE_INIT:
            my_printf(&huart1, "初始化中\r\n");
            break;
        case ESP01_STATE_CONNECTING:
            my_printf(&huart1, "连接中\r\n");
            break;
        case ESP01_STATE_CONNECTED:
            my_printf(&huart1, "已连接\r\n");
            break;
        case ESP01_STATE_ERROR:
            my_printf(&huart1, "错误状态\r\n");
            break;
        default:
            my_printf(&huart1, "未知状态\r\n");
            break;
    }

    my_printf(&huart1, "🔄 重试次数: %d\r\n", connection_retry_count);
    my_printf(&huart1, "⏰ 运行时间: %d秒\r\n", HAL_GetTick() / 1000);
    my_printf(&huart1, "📍 ThingSpeak频道: %s\r\n", THINGSPEAK_CHANNEL);
    my_printf(&huart1, "🗺️ OpenStreetMap: %s\r\n", OSM_MAP_URL);
    my_printf(&huart1, "🛣️ OSRM路径规划: %s\r\n", OSRM_ROUTING_URL);
    my_printf(&huart1, "===============================\r\n\r\n");
}

/**
 * 快速连接测试
 */
uint8_t esp01_QuickTest(void)
{
    my_printf(&huart1, "⚡ 快速连接测试...\r\n");

    // 测试第一个服务器
    Server_t* server = &servers[0];
    my_printf(&huart1, "🎯 测试服务器: %s\r\n", server->name);

    Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
    HAL_Delay(1000);

    Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"%s\",%d\r\n", server->host, server->port);
    HAL_Delay(5000);

    char quick_request[] = "GET / HTTP/1.1\r\nHost: httpbin.org\r\nConnection: close\r\n\r\n";
    int len = strlen(quick_request);

    Uart2_Printf(&huart2, "AT+CIPSEND=%d\r\n", len);
    HAL_Delay(1000);
    Uart2_Printf(&huart2, "%s", quick_request);
    HAL_Delay(3000);

    Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
    HAL_Delay(1000);

    my_printf(&huart1, "✅ 快速测试完成\r\n");
    return 1;
}

/**
 * 设置ESP01为已连接状态（兼容性函数）
 */
void esp01_SetConnected(void)
{
    wifi_connected = 1;
    tcp_connected = 1;
    esp01_state = ESP01_STATE_CONNECTED;
    my_printf(&huart1, "✅ ESP01状态设置为已连接\r\n");
}

// ==================== 旧版本兼容函数 ====================

/**
 * 启动初始化（兼容性函数）
 */
void esp01_StartInit(void)
{
    esp01_Init();
}

/**
 * 网络诊断（兼容性函数）
 */
void esp01_NetworkDiagnostics(void)
{
    esp01_NetworkDiagnostic();
}

/**
 * 强制重置（兼容性函数）
 */
void esp01_ForceReset(void)
{
    esp01_Reset();
}

/**
 * 尝试IP连接（兼容性函数）
 */
uint8_t esp01_TryTCPWithIP(void)
{
    return esp01_TestServerConnection();
}

/**
 * 设置TCP已连接（兼容性函数）
 */
void esp01_SetTCPConnected(void)
{
    tcp_connected = 1;
    my_printf(&huart1, "✅ TCP连接状态已设置\r\n");
}

/**
 * 设置数据发送就绪（兼容性函数）
 */
void esp01_SetDataSendReady(void)
{
    my_printf(&huart1, "✅ 数据发送就绪\r\n");
}

/**
 * 重置TCP状态（兼容性函数）
 */
void esp01_ResetTCPState(void)
{
    tcp_connected = 0;
    my_printf(&huart1, "🔄 TCP状态已重置\r\n");
}

/**
 * 根据路径数据获取目的地代码
 */
int esp01_GetDestinationCode(const char* route_data)
{
    if (!route_data) return 9999; // 默认万达

    // 调试：显示接收到的路径数据
    my_printf(&huart1, "🔍 调试: 接收到的路径数据 = [%s]\r\n", route_data);

    // 检查路径数据中的目的地信息（匹配英文名称）
    if (strstr(route_data, "wangda") || strstr(route_data, "wanda") || strstr(route_data, "万达")) {
        return 1111; // 万达广场
    }
    else if (strstr(route_data, "shuyuan") || strstr(route_data, "书院") || strstr(route_data, "酃湖书院")) {
        return 2222; // 酃湖书院
    }
    else if (strstr(route_data, "tiyuzhonxin") || strstr(route_data, "体育")) {
        return 3333; // 体育中心
    }
    else if (strstr(route_data, "huochezhan") || strstr(route_data, "gaotie") || strstr(route_data, "火车")) {
        return 4444; // 火车站
    }
    else if (strstr(route_data, "yiyuan") || strstr(route_data, "医院")) {
        return 5555; // 医院
    }

    return 9999; // 默认万达（向后兼容）
}

/**
 * 根据目的地代码获取目的地名称
 */
const char* esp01_GetDestinationName(int destination_code)
{
    switch (destination_code) {
        case 1111: return "万达广场(衡阳酃湖店)";
        case 2222: return "酃湖书院";
        case 3333: return "衡阳市体育中心";
        case 4444: return "衡阳火车站";
        case 5555: return "南华大学附属第一医院";
        case 9999: return "万达广场(兼容模式)";
        default: return "未知目的地";
    }
}

